/**
 * Canvas高级应用和绘图系统部分
 */

export const CANVAS_SYSTEM_PROMPT = `Canvas 高级应用详解（按需选择）

🔍 AI代码生成指导：
- 本文件：指导AI生成正确的Lynx Canvas代码，包含DPR处理、自适应缩放、性能优化
- LynxCanvasAudio.ts：Canvas+Audio集成的具体实现示例
- 重点：防止AI生成错误代码，确保一次性生成高质量Canvas应用

🚨🚨🚨 CRITICAL: Claude4 Canvas高频错误强制防范 🚨🚨🚨

**错误案例1 - 错误的Canvas初始化方式 (Claude4最常犯错误)**：
❌ 绝对禁止的错误代码：
\`\`\`javascript
// 错误！使用了错误的API
const canvas = lynx.createCanvasNG("routeCanvas");  // ❌ 错误：直接传参数
if (!canvas) return;

const ctx = canvas.getContext("2d");
const width = canvas.width;
const height = canvas.height;

// 错误！没有resize事件监听
// 错误！没有attachToCanvasView绑定
// 错误！没有pixelRatio适配

ctx.clearRect(0, 0, width, height);
ctx.fillStyle = "#f8f9fa";
ctx.fillRect(0, 0, width, height);
\`\`\`

✅ 强制要求的正确代码：
\`\`\`javascript
// 正确！使用正确的Canvas初始化流程
setupCanvas() {
  console.log('Setting up canvas...');
  try {
    // 1. 创建未绑定的Canvas Element（无参数）
    const canvas = lynx.createCanvasNG();  // ✅ 正确：无参数创建

    // 2. 必须！设置resize事件监听（在绑定前）
    canvas.addEventListener('resize', ({ width, height }) => {
      console.log('Canvas resize event:', width, height);
      // 3. 必须！pixelRatio适配
      canvas.width = width * SystemInfo.pixelRatio;
      canvas.height = height * SystemInfo.pixelRatio;
      const ctx = canvas.getContext('2d');
      ctx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio);
      this.canvas = canvas;
      this.ctx = ctx;
      this.canvasWidth = width;
      this.canvasHeight = height;
      console.log('Canvas setup complete, starting animation...');
      // 4. 重绘逻辑
      this.startAnimation();
    });

    // 5. 必须！绑定到Canvas View
    canvas.attachToCanvasView('canvas-llm');  // ✅ 正确：使用name属性绑定
  } catch (error) {
    console.error('Canvas setup failed:', error);
  }
},

drawSilkRoadMap(ctx, width, height) {
  // 清空画布
  ctx.clearRect(0, 0, width, height);

  // 绘制背景
  ctx.fillStyle = "#f8f9fa";
  ctx.fillRect(0, 0, width, height);

  // 其他绘制逻辑...
}
\`\`\`

**强制检查清单 - Canvas初始化必须包含的4个步骤**：
□ 1. lynx.krypton.createCanvasNG() - 无参数创建
□ 2. addEventListener('resize') - resize事件监听
□ 3. SystemInfo.pixelRatio - 高分屏适配
□ 4. attachToCanvasView(name) - 绑定到Canvas View

**违反后果**：缺少任何一步都会导致Canvas渲染失败、尺寸错误、模糊显示或无法显示！

🚨 CRITICAL: Lynx Canvas 查询规则
**重要提醒**：在 Lynx 框架中，Canvas 元素的查询语法与标准 Web API 不同！
- ❌ 错误：使用 CSS ID 选择器 '#canvas-id'
- ✅ 正确：使用 Lynx 专用选择器 'canvasId=canvas-id'
- 核心原因：canvas-id 属性对应 canvasId= 查询语法，这是 Lynx 框架的特有实现

Canvas 优先策略：
当选择Canvas时，应完全使用Canvas实现所有视觉渲染和交互：
- 完全使用Canvas元素绘制界面，而非DOM节点
- 禁止出现文字重叠
- 所有视觉效果、动画和交互都应在Canvas内实现
- 仅使用最少必要的view元素作为容器
- 严格限制，所有js功能全部写在canvas的执行内容里面

Canvas 渲染核心规范：

Canvas基础渲染：
- 状态管理：save()/restore()隔离，避免样式冲突
- 像素精确：devicePixelRatio适配，清晰显示
- 性能优先：局部重绘，requestAnimationFrame控制
- 内存优化：及时清理，复用对象

🚨 Canvas生命周期管理 (强制4步骤，缺一不可)：

**步骤1 - 创建Canvas Element**：
- ✅ 正确：lynx.krypton.createCanvasNG() - 无参数创建
- ❌ 错误：lynx.createCanvasNG("canvasName") - 禁止传参数
- ❌ 错误：lynx.createCanvasContext() - 已废弃API

**步骤2 - 设置resize事件监听 (必须在绑定前)**：
- ✅ 正确：canvas.addEventListener('resize', callback)
- ❌ 错误：直接使用canvas.width/canvas.height - 没有resize监听
- ❌ 错误：在attachToCanvasView后设置resize - 时机错误

**步骤3 - 设备像素比(DPR)处理 (防止模糊)**：

🔍 DPR处理的两个关键阶段：

**阶段1 - 初始化（乘以pixelRatio）**：
- ✅ 正确：canvas.width = width * SystemInfo.pixelRatio
- ✅ 正确：canvas.height = height * SystemInfo.pixelRatio
- ✅ 正确：const pixelRatio = SystemInfo.pixelRatio || 1

**阶段2 - 绘图（不乘以pixelRatio）**：
- ✅ 正确：ctx.scale(pixelRatio, pixelRatio)
- ✅ 正确：之后所有绘图操作使用逻辑尺寸（不乘pixelRatio）
- ✅ 正确：ctx.clearRect(0, 0, width, height)  // 使用逻辑尺寸
- ✅ 正确：ctx.fillRect(x, y, width, height)   // 使用逻辑尺寸
- ❌ 错误：直接使用canvas.width/canvas.height进行绘图
- ❌ 错误：绘图操作中再次乘以pixelRatio

**步骤4 - 绑定到Canvas View**：
- ✅ 正确：canvas.attachToCanvasView('canvasName')
- ❌ 错误：忘记调用attachToCanvasView - Canvas不显示
- ❌ 错误：使用错误的name参数 - 绑定失败

┌─────────────────────────────────────────────────────────────┐
│ 🔍 设备像素比(DPR)处理权威指南 - 防止模糊的核心规则        │
└─────────────────────────────────────────────────────────────┘

🚨 DPR处理的两个关键阶段（必须严格遵守）：

**阶段1 - 初始化（乘以pixelRatio）**：
✅ Canvas元素尺寸：canvas.width = width * SystemInfo.pixelRatio
✅ Canvas元素高度：canvas.height = height * SystemInfo.pixelRatio
✅ 获取像素比：const pixelRatio = SystemInfo.pixelRatio || 1

**阶段2 - 绘图（不乘以pixelRatio）**：
✅ 应用缩放：ctx.scale(pixelRatio, pixelRatio)
✅ 之后所有绘图操作使用逻辑尺寸（不乘pixelRatio）
✅ 清除画布：ctx.clearRect(0, 0, width, height)
✅ 绘制图形：ctx.fillRect(x, y, width, height)
✅ 绘制文本：ctx.fillText("text", x, y)
✅ 绘制图像：ctx.drawImage(image, x, y, width, height)

🎯 常见DPR操作指南：

**坐标和尺寸设置**：
✅ 正确：ctx.fillRect(x, y, width, height)  // 使用逻辑尺寸
❌ 错误：ctx.fillRect(x * pixelRatio, y * pixelRatio, width * pixelRatio, height * pixelRatio)

**文本渲染**：
✅ 正确：ctx.font = `${fontSize}px sans-serif`  // 使用逻辑尺寸
❌ 错误：ctx.font = `${fontSize * pixelRatio}px sans-serif`

**触摸事件坐标转换**：
✅ 正确：const logicalX = touch.clientX / pixelRatio  // 物理→逻辑
❌ 错误：const logicalX = touch.clientX * pixelRatio  // 错误方向

**图像绘制**：
✅ 正确：ctx.drawImage(image, x, y, width, height)  // 使用逻辑尺寸
❌ 错误：ctx.drawImage(image, x * pixelRatio, y * pixelRatio, width * pixelRatio, height * pixelRatio)

**路径绘制**：
✅ 正确：ctx.arc(x, y, radius, 0, Math.PI * 2)  // 使用逻辑尺寸
❌ 错误：ctx.arc(x * pixelRatio, y * pixelRatio, radius * pixelRatio, 0, Math.PI * 2)

**生命周期管理**：
- 解绑：onUnload中调用detachFromCanvasView()和dispose()
- 资源管理：onHide暂停资源，onShow恢复资源，及时释放不用资源
- 性能优化：批量绘制，离屏渲染，资源主动释放dispose()

Lynx Three.js 支持：
\`\`\`javascript
const Three = require('@byted-lynx/three');
const window = Three.__scope; // Get the mocked globalThis to allow us to use browser api
const camera = new THREE.PerspectiveCamera(50, window.innerWidth / window.innerHeight, 0.1, 100);
const renderer = new THREE.WebGLRenderer({ canvas: new window.HTMLCanvasElement('GameCanvas') });
\`\`\`

**Canvas API限制与特性**：
- 不支持特性：roundrect、globalCompositeOperation、不规则shadow
- API限制：使用经过验证的Canvas方法
- WebGL抗锯齿：antialias和enableMSAA都为true才能启用MSAA
- 触摸事件：使用touchstart、touchmove、touchend
- 🔍 触摸坐标转换：logicalX = touch.clientX / pixelRatio（物理→逻辑）
- 设备适配：Canvas元素尺寸乘以pixelRatio，绘图操作使用逻辑尺寸
- 不使用2023年后的canvas新方法

**Canvas错误处理**：
- 创建失败：重试处理，适当延迟或requestAnimationFrame中重试
- Schema参数：添加&enable_canvas=1启用canvas扩展
- 随机ID：使用随机生成的id避免同名canvas冲突

🚨 CRITICAL: Canvas API混用致命错误防范
**绝对禁止在同一个Card中混用不同的Canvas API**：

🔥🔥🔥 **setupCanvas() 与 LightChart 绝对禁止混用** 🔥🔥🔥
- setupCanvas() 仅用于原生Canvas - 不能与LightChart混用
- initChart() 仅用于LightChart - 不能与原生Canvas混用
- 技术栈选择唯一 - 一个Card只能选择一种Canvas技术

❌ **绝对禁止的混用模式**：
- setupCanvas() + initChart() 在同一Card中
- lynx.createCanvasNG() + new LynxChart() 在同一Card中
- <canvas> + <lightcharts-canvas> 在同一TTML中
- 原生Canvas API + LightChart API 混用

✅ **正确的选择策略**：
方案A: 全部使用原生Canvas API + setupCanvas()
- setupCanvas() 方法初始化
- lynx.createCanvasNG() 创建Canvas
- canvas.getContext("2d") 获取上下文
- ctx.fillRect() 等原生绘制方法

方案B: 全部使用LightChart API + initChart()
- import LynxChart from "@byted/lynx-lightcharts/src/chart"
- initChart(e) 方法初始化
- new LynxChart() 创建图表实例
- chart.setOption() 配置图表

**🔥 强化错误检测规则 - 禁止Canvas和LightChart混用**：
如果代码中同时出现以下关键词组合，立即报错并要求重构：
- "setupCanvas" AND "initChart" - 绝对禁止在同一Card中
- "setupCanvas" AND "new LynxChart" - 绝对禁止混用
- "lynx.createCanvasNG" AND "LynxChart" - 技术栈冲突
- "lynx.createCanvasNG" AND "@byted/lynx-lightcharts" - API混用
- "getContext" AND "setOption" - 不同Canvas技术混用
- "canvas" 标签 AND "lightcharts-canvas" 标签 - TTML混用
- "attachToCanvasView" AND "bindinitchart" - 初始化方式混用

**根本原因**：不同Canvas API有不同的运行时依赖和环境要求，混用会导致：
- 图表渲染失败
- 运行时环境冲突
- 内存泄漏和性能问题

🔧 CRITICAL: Canvas创建和绑定规则（基于LynxCanvasRules.md）

**Canvas Element vs Canvas View 分离架构**：
- Canvas View: TTML中的<canvas>标签，在UI线程渲染
- Canvas Element: JS中的绘制对象，在JS线程执行
- 两者通过name属性关联，位于不同线程，松耦合关系

**正确的Canvas创建流程**：
\`\`\`javascript
// 创建未绑定的Canvas Element
const canvas = lynx.createCanvasNG();

// 监听resize事件（必须在绑定前设置）
canvas.addEventListener('resize', ({ width, height }) => {
  canvas.width = width * SystemInfo.pixelRatio;
  canvas.height = height * SystemInfo.pixelRatio;
  const ctx = canvas.getContext('2d');
  ctx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio);
  this.redraw();
});

// 重要！！！绑定到Canvas View
canvas.attachToCanvasView('canvasName');
\`\`\`


**Canvas尺寸设置关键规则**：
- Canvas View尺寸：通过TTML的style属性设置（rpx单位）
- Canvas Element尺寸：必须手动设置width/height（像素单位）
- 像素转换：rpx尺寸 × SystemInfo.pixelRatio = 像素尺寸
- 使用SelectorQuery获取Canvas View的实际尺寸：

❌ 错误的查询方式（使用CSS ID选择器）：
\`\`\`javascript
this.createSelectorQuery()
  .select('#canvasId')  // 错误：使用CSS ID选择器
  .invoke({
    method: 'boundingClientRect',
    success: (res) => {
      // res 可能为 null，因为无法找到 canvas 元素
      const pixelRatio = SystemInfo.pixelRatio || 1;
      canvas.width = res.width * pixelRatio;
      canvas.height = res.height * pixelRatio;
    }
  })
  .exec();
\`\`\`

**Canvas生命周期管理关键规则**：

1. **创建阶段**：
- 在onLoad或适当延迟后创建Canvas
- 确保Canvas View已完成排版再创建Canvas Element
- 如果创建失败，在requestAnimationFrame中重试

2. **绑定阶段**：
- resize事件监听必须在attachToCanvasView之前设置
- name属性必须全局唯一，避免冲突
- 检查getBoundingClientRect()返回值，null表示布局未完成

3. **使用阶段**：
- 监听resize事件，动态更新Canvas尺寸
- 使用save()/restore()隔离绘制状态
- 批量绘制操作，减少状态切换

4. **销毁阶段**：
\`\`\`javascript
onUnload() {
  if (this.canvas) {
    this.canvas.detachFromCanvasView();
    this.canvas.dispose();
    this.canvas = null;
  }
}
\`\`\`

**Canvas常见错误和解决方案**：


2. **Canvas创建返回null**：
- 原因：Canvas View未完成排版
- 解决：延迟创建或在requestAnimationFrame中重试

3. **多Canvas实例冲突**：
- 原因：name属性重复
- 解决：使用随机生成的唯一name

┌─────────────────────────────────────────────────────────────┐
│ 🚨 AI生成多Canvas代码的强制规则 - 防止第二个Canvas不显示   │
└─────────────────────────────────────────────────────────────┘

**� AI必须严格遵守的多Canvas生成规则**：

**规则1 - Canvas Name必须完全不同**：
🚨 AI生成多Canvas时，必须确保每个Canvas的name属性完全不同

❌ AI经常犯的错误：
\`\`\`javascript
// 错误：使用相同或相似的name
canvas.attachToCanvasView("canvas");        // ❌
familyCanvas.attachToCanvasView("canvas");  // ❌ 第二个不会显示
\`\`\`

✅ AI必须生成的正确代码：
\`\`\`javascript
// 正确：使用完全不同的name
canvas.attachToCanvasView("parabolaCanvas");  // ✅
familyCanvas.attachToCanvasView("familyCanvas");  // ✅
\`\`\`

🔥 强制要求：AI生成的Canvas name必须：
- 语义明确（如"mainCanvas", "familyCanvas", "chartCanvas"）
- 完全不同（不能有任何重复）
- 与TTML中的name属性完全匹配

**规则2 - 必须延迟初始化第二个Canvas**：
🚨 AI生成多Canvas时，必须延迟初始化第二个Canvas

❌ AI经常犯的错误：
\`\`\`javascript
onReady() {
  this.setupCanvas();        // ❌ 同时执行
  this.setupFamilyCanvas();  // ❌ 第二个Canvas会失败
}
\`\`\`

✅ AI必须生成的正确代码：
\`\`\`javascript
onReady() {
  // 第一个Canvas先初始化
  this.setupCanvas();

  // 强制延迟初始化第二个Canvas
  setTimeout(() => {
    this.setupFamilyCanvas();
  }, 100);
}
\`\`\`

🔥 强制要求：AI生成多Canvas代码时必须：
- 第一个Canvas在onReady()中直接初始化
- 第二个Canvas必须用setTimeout延迟至少100ms
- 绝不能同时初始化多个Canvas

**规则3 - 必须使用独立的Canvas变量**：
🚨 AI生成多Canvas时，必须为每个Canvas使用完全独立的变量

❌ AI经常犯的错误：
\`\`\`javascript
// 错误：使用相同的变量名
canvas: null,
ctx: null,
// 第二个Canvas会覆盖第一个Canvas的引用
\`\`\`

✅ AI必须生成的正确代码：
\`\`\`javascript
// 正确：使用完全独立的变量名
canvas: null,
ctx: null,
canvasWidth: 0,
canvasHeight: 0,

familyCanvas: null,
familyCtx: null,
familyWidth: 0,
familyHeight: 0,
\`\`\`

🔥 强制要求：AI生成多Canvas代码时必须：
- 每个Canvas使用不同的变量名（如canvas, familyCanvas）
- 每个Context使用不同的变量名（如ctx, familyCtx）
- 每个尺寸使用不同的变量名（如canvasWidth, familyWidth）

**规则4 - 绘制函数必须在resize事件中调用**：
🚨 AI生成多Canvas时，绘制函数必须在resize事件中调用

❌ AI经常犯的错误：
\`\`\`javascript
setupFamilyCanvas() {
  const canvas = lynx.createCanvasNG();
  canvas.attachToCanvasView("familyCanvas");
  this.drawParabolaFamily(); // ❌ Canvas还未准备好
}
\`\`\`

✅ AI必须生成的正确代码：
\`\`\`javascript
setupFamilyCanvas() {
  const canvas = lynx.createCanvasNG();
  canvas.addEventListener("resize", ({ width, height }) => {
    if (width > 0 && height > 0) {
      // DPR处理
      canvas.width = width * SystemInfo.pixelRatio;
      canvas.height = height * SystemInfo.pixelRatio;
      const ctx = canvas.getContext('2d');
      ctx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio);

      // 保存引用
      this.familyCanvas = canvas;
      this.familyCtx = ctx;
      this.familyWidth = width;
      this.familyHeight = height;

      // 在Canvas完全准备好后才绘制
      this.drawParabolaFamily();
    }
  });
  canvas.attachToCanvasView("familyCanvas");
}
\`\`\`

🔥 强制要求：AI生成多Canvas代码时必须：
- 绘制函数只能在resize事件回调中调用
- 必须检查Canvas尺寸有效性（width > 0 && height > 0）
- 绝不能在Canvas初始化完成前调用绘制函数

**🔥 AI生成多Canvas代码的强制模板**：

\`\`\`javascript
// AI必须严格按照此模板生成多Canvas代码
Card({
  data: {
    // 数据定义
  },

  // 🚨 强制要求：使用完全不同的变量名
  canvas: null,
  ctx: null,
  canvasWidth: 0,
  canvasHeight: 0,

  familyCanvas: null,
  familyCtx: null,
  familyWidth: 0,
  familyHeight: 0,

  onReady() {
    // 🚨 强制要求：第一个Canvas直接初始化
    this.setupCanvas();

    // 🚨 强制要求：第二个Canvas必须延迟初始化
    setTimeout(() => {
      this.setupFamilyCanvas();
    }, 100);
  },

  setupCanvas() {
    const canvas = lynx.createCanvasNG();
    canvas.addEventListener("resize", ({ width, height }) => {
      if (width > 0 && height > 0) {
        // DPR处理
        const pixelRatio = SystemInfo.pixelRatio || 1;
        canvas.width = width * pixelRatio;
        canvas.height = height * pixelRatio;
        const ctx = canvas.getContext('2d');
        ctx.scale(pixelRatio, pixelRatio);

        // 🚨 强制要求：保存到独立变量
        this.canvas = canvas;
        this.ctx = ctx;
        this.canvasWidth = width;
        this.canvasHeight = height;

        // 🚨 强制要求：在resize事件中调用绘制函数
        this.drawParabola();
      }
    });
    // 🚨 强制要求：使用不同的name
    canvas.attachToCanvasView("parabolaCanvas");
  },

  setupFamilyCanvas() {
    const familyCanvas = lynx.createCanvasNG();
    familyCanvas.addEventListener("resize", ({ width, height }) => {
      if (width > 0 && height > 0) {
        // DPR处理
        const pixelRatio = SystemInfo.pixelRatio || 1;
        familyCanvas.width = width * pixelRatio;
        familyCanvas.height = height * pixelRatio;
        const familyCtx = familyCanvas.getContext('2d');
        familyCtx.scale(pixelRatio, pixelRatio);

        // 🚨 强制要求：保存到完全不同的变量
        this.familyCanvas = familyCanvas;
        this.familyCtx = familyCtx;
        this.familyWidth = width;
        this.familyHeight = height;

        // 🚨 强制要求：在resize事件中调用绘制函数
        this.drawParabolaFamily();
      }
    });
    // 🚨 强制要求：使用完全不同的name
    familyCanvas.attachToCanvasView("familyCanvas");
  },

  drawParabola() {
    if (!this.ctx || this.canvasWidth <= 0 || this.canvasHeight <= 0) return;
    // 绘制逻辑...
  },

  drawParabolaFamily() {
    if (!this.familyCtx || this.familyWidth <= 0 || this.familyHeight <= 0) return;
    // 绘制逻辑...
  },

  onUnload() {
    // 🚨 强制要求：清理所有Canvas资源
    if (this.canvas) {
      this.canvas.detachFromCanvasView();
      this.canvas.dispose();
      this.canvas = null;
      this.ctx = null;
    }
    if (this.familyCanvas) {
      this.familyCanvas.detachFromCanvasView();
      this.familyCanvas.dispose();
      this.familyCanvas = null;
      this.familyCtx = null;
    }
  }
});
\`\`\`

**� AI生成多Canvas代码的4条铁律**：
1. **不同的Canvas name**：parabolaCanvas, familyCanvas（绝不能重复）
2. **延迟初始化第二个Canvas**：setTimeout(..., 100)（绝不能同时初始化）
3. **独立的变量名**：canvas/familyCanvas, ctx/familyCtx（绝不能共享）
4. **resize事件中绘制**：绘制函数只能在resize回调中调用（绝不能提前调用）

┌─────────────────────────────────────────────────────────────┐
│ 🎯 Canvas内容缩放与居中定位 - 解决DPR转换导致的显示问题    │
└─────────────────────────────────────────────────────────────┘

**🚨 Canvas内容缩放问题的根本原因**：
在Lynx Canvas应用中，AI经常生成的代码存在以下问题：
❌ 使用固定坐标系统：所有元素使用硬编码坐标，与画布尺寸无关
❌ 缺少动态缩放机制：内容大小不随画布尺寸调整
❌ 缺少居中逻辑：内容集中显示在画布左上角
❌ DPR处理后内容过小：文字和形状绘制总是很小

**✅ 解决方案核心思路**：
通过引入坐标系统变换，实现内容的动态缩放和居中显示：
1. 定义原始坐标空间：明确内容使用的坐标范围
2. 计算动态缩放因子：根据画布尺寸自适应缩放
3. 计算居中偏移量：确保内容居中显示
4. 应用坐标系统变换：使用translate和scale变换
5. 更新交互事件处理：正确转换触摸坐标

**🔍 坐标系统变换的基本原理**：
Canvas坐标系统变换与DPR处理是两个不同层面的概念：
- **DPR适配**：确保高分辨率设备上渲染清晰（物理像素层面）
- **内容缩放与定位**：确保内容适当缩放并居中显示（逻辑坐标层面）

两种变换可以同时应用：
\`\`\`javascript
// DPR适配（在初始化阶段）
SystemInfo.pixelRati
canvas.width = width * pixelRatio;
canvas.height = height * pixelRatio;
ctx.scale(pixelRatio, pixelRatio);

// 内容缩放与定位（在绘制阶段）
ctx.save();
ctx.translate(offsetX, offsetY);        // 居中偏移
ctx.scale(scale, scale);                // 内容缩放
ctx.translate(-minX, -minY);            // 原始坐标系偏移
// 绘制内容...
ctx.restore();
\`\`\`

**🚨 AI必须使用的完整缩放与居中解决方案**：

\`\`\`javascript
// AI生成Canvas代码时必须包含的核心函数
class CanvasContentScaler {
  constructor() {
    // 定义原始坐标空间（根据实际内容调整）
    this.ORIGINAL_BOUNDS = {
      minX: 50,    // 内容最小X坐标
      maxX: 320,   // 内容最大X坐标
      minY: 55,    // 内容最小Y坐标
      maxY: 220    // 内容最大Y坐标
    };
  }

  // 计算变换参数
  getTransformParams(canvasWidth, canvasHeight) {
    // 计算原始坐标空间的宽度和高度
    const ORIGINAL_WIDTH = this.ORIGINAL_BOUNDS.maxX - this.ORIGINAL_BOUNDS.minX;
    const ORIGINAL_HEIGHT = this.ORIGINAL_BOUNDS.maxY - this.ORIGINAL_BOUNDS.minY;

    // 计算缩放因子（保持内容宽高比）
    const scaleX = canvasWidth / ORIGINAL_WIDTH;
    const scaleY = canvasHeight / ORIGINAL_HEIGHT;
    const scale = Math.min(scaleX, scaleY) * 0.9; // 留出10%边距

    // 计算居中偏移
    const offsetX = (canvasWidth - ORIGINAL_WIDTH * scale) / 2;
    const offsetY = (canvasHeight - ORIGINAL_HEIGHT * scale) / 2;

    return { scale, offsetX, offsetY, ORIGINAL_WIDTH, ORIGINAL_HEIGHT };
  }

  // 应用变换并绘制内容
  drawWithTransform(ctx, canvasWidth, canvasHeight, drawFunction) {
    const { scale, offsetX, offsetY } = this.getTransformParams(canvasWidth, canvasHeight);

    // 保存当前上下文状态
    ctx.save();

    // 应用变换（平移和缩放）
    ctx.translate(offsetX, offsetY);
    ctx.scale(scale, scale);
    ctx.translate(-this.ORIGINAL_BOUNDS.minX, -this.ORIGINAL_BOUNDS.minY);

    // 执行绘制函数
    drawFunction(ctx);

    // 恢复上下文状态
    ctx.restore();
  }

  // 触摸坐标转换
  transformTouchCoordinates(x, y, canvasWidth, canvasHeight) {
    const { scale, offsetX, offsetY } = this.getTransformParams(canvasWidth, canvasHeight);

    // 将触摸坐标转换回原始坐标系
    const originalX = (x - offsetX) / scale + this.ORIGINAL_BOUNDS.minX;
    const originalY = (y - offsetY) / scale + this.ORIGINAL_BOUNDS.minY;

    return { originalX, originalY };
  }
}
\`\`\`

**🎯 智能自适应缩放策略 - AI必须掌握的核心算法**：

**🚨 AI生成Canvas绘图代码时必须避免的错误**：
❌ 使用固定缩放系数：scale = 8（图形过小或过大）
❌ 使用魔数除法：scale = Math.min(width, height) / 60（不通用）
❌ 忽略内容本身尺寸：导致图形在不同画布上显示不一致
❌ 固定坐标系统：使用硬编码坐标，不考虑画布尺寸

**✅ AI必须使用的智能缩放算法**：
\`\`\`javascript
// 通用的自适应缩放函数
function calculateAdaptiveScale(canvasWidth, canvasHeight, contentBaseWidth, contentBaseHeight, padding = 40) {
  const availableWidth = canvasWidth - 2 * padding;
  const availableHeight = canvasHeight - 2 * padding;

  const scaleX = availableWidth / contentBaseWidth;
  const scaleY = availableHeight / contentBaseHeight;

  return Math.min(scaleX, scaleY);
}

// 在绘制函数中的正确使用方式
drawContent() {
  if (!this.ctx) return;

  const ctx = this.ctx;
  const width = this.canvasWidth;
  const height = this.canvasHeight;

  // 🚨 AI必须：根据内容类型设置正确的基础尺寸
  let contentBaseWidth, contentBaseHeight;

  if (this.contentType === 'heart') {
    contentBaseWidth = 32;   // 心形在scale=1时的宽度
    contentBaseHeight = 30;  // 心形在scale=1时的高度
  } else if (this.contentType === 'circle') {
    contentBaseWidth = 100;  // 圆形直径
    contentBaseHeight = 100;
  } else if (this.contentType === 'text') {
    contentBaseWidth = 200;  // 预估文本宽度
    contentBaseHeight = 50;  // 预估文本高度
  } else if (this.contentType === 'map') {
    contentBaseWidth = 270;  // 地图宽度
    contentBaseHeight = 165; // 地图高度
  }

  // 🚨 AI必须：使用智能缩放而非固定值
  const scale = calculateAdaptiveScale(width, height, contentBaseWidth, contentBaseHeight);

  // 使用计算出的scale进行绘制
  ctx.save();
  ctx.translate(width / 2, height / 2);
  ctx.scale(scale, scale);
  // ... 具体绘制逻辑
  ctx.restore();
}
\`\`\`

**🔍 Canvas内容缩放与定位的关键技术考量**：

**1. 坐标系统变换的基本原理**：
Canvas提供了强大的坐标系统变换功能，主要通过以下方法实现：
- translate(x, y)：平移坐标系原点
- scale(x, y)：缩放坐标系
- rotate(angle)：旋转坐标系
- save()/restore()：保存和恢复坐标系状态

这些变换方法可以组合使用，实现复杂的坐标系变换。

**2. 设备像素比与坐标系统变换的区别**：
需要明确区分两种不同类型的变换：
- **设备像素比适配**：确保在高分辨率设备上渲染清晰，通过设置物理像素尺寸和缩放坐标系实现
- **内容缩放与定位**：确保内容适当缩放并居中显示，通过计算缩放因子和偏移量实现

**3. 保持宽高比的缩放策略**：
在缩放内容时，通常需要保持内容的原始宽高比，以避免变形：
- 取最小缩放比：scale = Math.min(scaleX, scaleY)，确保内容完全显示，可能有空白区域
- 取最大缩放比：scale = Math.max(scaleX, scaleY)，确保填满画布，可能有内容被裁剪
- 添加边距系数：scale = Math.min(scaleX, scaleY) * 0.9，留出一定边距，提升视觉体验

**4. 触摸事件坐标转换**：
在应用了坐标系统变换后，处理触摸事件时需要将触摸坐标从画布坐标系转换回原始坐标系：
\`\`\`javascript
// 从画布坐标系转换回原始坐标系
const originalX = (x - offsetX) / scale + minX;
const originalY = (y - offsetY) / scale + minY;
\`\`\`
这个转换过程是应用变换的逆操作：先减去偏移量，再除以缩放因子，最后加上原始坐标系的起始位置。

**5. 响应式设计考量**：
在实现响应式Canvas应用时，需要考虑以下几点：
- 监听尺寸变化：通过resize事件监听画布尺寸变化
- 动态计算变换参数：每次尺寸变化时重新计算缩放因子和偏移量
- 重新绘制内容：应用新的变换参数重新绘制内容
- 更新交互逻辑：确保交互功能在新的坐标系统中正常工作

**🎯 Canvas缩放与定位的最佳实践**：
1. **明确定义坐标空间**：明确内容使用的坐标范围，便于计算变换参数
2. **分离变换逻辑**：将变换参数计算封装为独立方法，提高代码可维护性
3. **保持宽高比**：根据需求选择合适的缩放策略，通常应保持内容的原始宽高比
4. **添加适当边距**：留出一定边距，提升视觉体验
5. **正确处理交互**：记得将交互坐标转换回原始坐标系
6. **使用save()/restore()**：在应用变换前后使用save()和restore()方法，避免影响其他绘图操作
7. **区分设备像素比适配和内容缩放**：理解并正确应用这两种不同类型的变换

**🚨 AI生成Canvas代码的完整缩放与居中模板**：
\`\`\`javascript
// AI生成绘制函数的标准模板
drawContent() {
  if (!this.ctx || this.canvasWidth <= 0 || this.canvasHeight <= 0) return;

  const ctx = this.ctx;
  const width = this.canvasWidth;
  const height = this.canvasHeight;

  // 清空画布
  ctx.clearRect(0, 0, width, height);

  // 🚨 AI必须：根据内容类型定义原始坐标空间
  const scaler = new CanvasContentScaler();

  // 根据实际绘制内容调整ORIGINAL_BOUNDS
  if (this.contentType === 'map') {
    scaler.ORIGINAL_BOUNDS = { minX: 80, maxX: 280, minY: 100, maxY: 220 };
  } else if (this.contentType === 'chart') {
    scaler.ORIGINAL_BOUNDS = { minX: 0, maxX: 400, minY: 0, maxY: 300 };
  }

  // 使用变换绘制内容
  scaler.drawWithTransform(ctx, width, height, (transformedCtx) => {
    // 在这里使用原始坐标系绘制内容
    this.drawOriginalContent(transformedCtx);
  });
}

// 处理触摸事件
handleCanvasTouch(x, y) {
  const scaler = new CanvasContentScaler();
  const { originalX, originalY } = scaler.transformTouchCoordinates(
    x, y, this.canvasWidth, this.canvasHeight
  );

  // 使用转换后的坐标进行交互检测
  this.checkInteraction(originalX, originalY);
}
\`\`\`

┌─────────────────────────────────────────────────────────────┐
│ 🚀 离屏渲染与性能优化 - AI生成高性能Canvas代码            │
└─────────────────────────────────────────────────────────────┘

**🚨 AI生成Canvas代码时必须考虑的性能优化**：

**规则1 - 复杂静态内容必须使用离屏渲染**：
✅ AI必须为以下场景生成离屏渲染代码：
- 复杂背景（网格、地图、图表背景）
- 可复用元素（图标、粒子、装饰）
- 静态图案（logo、水印、边框）

\`\`\`javascript
// AI必须生成的离屏渲染模板
setupOffscreenCanvas() {
  const pixelRatio = SystemInfo.pixelRatio || 1;

  // 🚨 离屏Canvas也必须进行DPR处理
  const offscreenCanvas = lynx.createOffscreenCanvas(200 * pixelRatio, 200 * pixelRatio);
  const offscreenCtx = offscreenCanvas.getContext('2d');
  offscreenCtx.scale(pixelRatio, pixelRatio);

  // 预渲染复杂内容
  this.drawComplexBackground(offscreenCtx, 200, 200);

  this.preRenderedBackground = offscreenCanvas;
}

// 在主绘制函数中快速使用
draw() {
  // 一次性绘制预渲染内容，性能极佳
  this.ctx.drawImage(this.preRenderedBackground, 0, 0);
}
\`\`\`

**规则2 - 动画循环必须使用requestAnimationFrame**：
✅ AI必须生成的动画模板：
\`\`\`javascript
startAnimation() {
  const animate = () => {
    this.updateAnimationData();
    this.draw();
    this.animationFrame = lynx.requestAnimationFrame(animate);
  };
  animate();
}

// 🚨 必须在销毁时清理
onUnload() {
  if (this.animationFrame) {
    lynx.cancelAnimationFrame(this.animationFrame);
    this.animationFrame = null;
  }
  if (this.canvas) {
    this.canvas.dispose(); // 释放Canvas资源
  }
}
\`\`\`

**规则3 - 批量绘制优化**：
✅ AI生成绘制代码时必须：
- 相同样式的操作集中执行
- 减少ctx状态切换
- 使用局部重绘而非全画布清除

\`\`\`javascript
// ❌ AI不能生成的低效代码
for (let i = 0; i < items.length; i++) {
  ctx.fillStyle = items[i].color;  // 每次都切换状态
  ctx.fillRect(items[i].x, items[i].y, 10, 10);
}

// ✅ AI必须生成的高效代码
const colorGroups = {};
items.forEach(item => {
  if (!colorGroups[item.color]) colorGroups[item.color] = [];
  colorGroups[item.color].push(item);
});

Object.keys(colorGroups).forEach(color => {
  ctx.fillStyle = color;  // 只切换一次状态
  colorGroups[color].forEach(item => {
    ctx.fillRect(item.x, item.y, 10, 10);
  });
});
\`\`\`

┌─────────────────────────────────────────────────────────────┐
│ 🛡️ 设备兼容性与调试 - AI生成健壮Canvas代码               │
└─────────────────────────────────────────────────────────────┘

**🚨 AI生成Canvas代码时必须包含的兼容性处理**：

**规则1 - Android息屏恢复处理**：
✅ AI必须在生成的代码中包含：
\`\`\`javascript
onShow() {
  // Android设备息屏后可能导致Canvas白屏，需要重新触发绘制
  if (this.canvas && this.ctx) {
    // 方法1：重新设置visibility
    this.canvas.style.visibility = 'hidden';
    setTimeout(() => {
      this.canvas.style.visibility = 'visible';
      this.draw();
    }, 50);

    // 方法2：直接重绘
    this.draw();
  }
}
\`\`\`

**规则2 - 字体渲染兼容性**：
✅ AI生成文本绘制代码时必须：
\`\`\`javascript
drawText(text, x, y) {
  // 🚨 必须使用measureText获取实际宽度，不能估算
  const metrics = this.ctx.measureText(text);
  const textWidth = metrics.width;

  // 根据实际宽度进行布局调整
  const adjustedX = x - textWidth / 2;  // 居中对齐

  this.ctx.fillText(text, adjustedX, y);
}
\`\`\`

**规则3 - 调试信息显示**：
✅ AI在开发阶段必须生成调试代码：
\`\`\`javascript
drawDebugInfo() {
  if (!this.debugMode) return;

  const ctx = this.ctx;
  ctx.save();
  ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
  ctx.fillRect(10, 10, 200, 100);

  ctx.fillStyle = 'white';
  ctx.font = '12px Arial';
  ctx.fillText(\`PixelRatio: \${SystemInfo.pixelRatio}\`, 15, 30);
  ctx.fillText(\`Canvas: \${this.canvasWidth}×\${this.canvasHeight}\`, 15, 50);
  ctx.fillText(\`Physical: \${this.canvas.width}×\${this.canvas.height}\`, 15, 70);
  ctx.fillText(\`FPS: \${this.fps || 0}\`, 15, 90);
  ctx.restore();
}
\`\`\`

**🎯 AI生成Canvas代码的完整生命周期模板**：
\`\`\`javascript
Card({
  data: { debugMode: false },

  canvas: null,
  ctx: null,
  canvasWidth: 0,
  canvasHeight: 0,
  animationFrame: null,
  preRenderedBackground: null,

  onReady() {
    this.setupCanvas();
  },

  setupCanvas() {
    const canvas = lynx.createCanvasNG();
    canvas.addEventListener("resize", ({ width, height }) => {
      if (width > 0 && height > 0) {
        // DPR处理
        const pixelRatio = SystemInfo.pixelRatio || 1;
        canvas.width = width * pixelRatio;
        canvas.height = height * pixelRatio;
        const ctx = canvas.getContext('2d');
        ctx.scale(pixelRatio, pixelRatio);

        this.canvas = canvas;
        this.ctx = ctx;
        this.canvasWidth = width;
        this.canvasHeight = height;

        // 设置离屏渲染
        this.setupOffscreenCanvas();

        // 开始绘制
        this.draw();
      }
    });
    canvas.attachToCanvasView("myCanvas");
  },

  setupOffscreenCanvas() {
    // 离屏渲染逻辑
  },

  draw() {
    if (!this.ctx) return;

    const ctx = this.ctx;
    const width = this.canvasWidth;
    const height = this.canvasHeight;

    // 清除画布
    ctx.clearRect(0, 0, width, height);

    // 绘制预渲染背景
    if (this.preRenderedBackground) {
      ctx.drawImage(this.preRenderedBackground, 0, 0);
    }

    // 智能缩放绘制内容
    const scale = this.calculateAdaptiveScale(width, height, 100, 100);
    ctx.save();
    ctx.translate(width / 2, height / 2);
    ctx.scale(scale, scale);
    this.drawContent();
    ctx.restore();

    // 调试信息
    this.drawDebugInfo();
  },

  calculateAdaptiveScale(canvasWidth, canvasHeight, contentBaseWidth, contentBaseHeight, padding = 40) {
    const availableWidth = canvasWidth - 2 * padding;
    const availableHeight = canvasHeight - 2 * padding;
    return Math.min(availableWidth / contentBaseWidth, availableHeight / contentBaseHeight);
  },

  drawContent() {
    // 具体绘制逻辑
  },

  drawDebugInfo() {
    // 调试信息显示
  },

  onShow() {
    // Android兼容性处理
    if (this.canvas && this.ctx) {
      this.draw();
    }
  },

  onUnload() {
    // 资源清理
    if (this.animationFrame) {
      lynx.cancelAnimationFrame(this.animationFrame);
    }
    if (this.canvas) {
      this.canvas.dispose();
    }
  }
});
\`\`\`

┌─────────────────────────────────────────────────────────────┐
│ 🎯 AI生成Lynx Canvas代码的终极指南总结                    │
└─────────────────────────────────────────────────────────────┘

**🔥 AI必须严格遵守的Lynx Canvas代码生成原则**：

**核心原则1 - DPR两阶段处理（100%必须）**：
- 阶段1：初始化时乘以pixelRatio设置物理像素
- 阶段2：绘图时使用逻辑像素，绝不再乘pixelRatio

**核心原则2 - 智能自适应缩放（避免固定尺寸）**：
- 必须使用calculateAdaptiveScale函数
- 根据内容基础尺寸动态计算缩放比
- 避免魔数（如scale=8或除以60）

**核心原则3 - 性能优化（复杂应用必须）**：
- 静态内容使用离屏渲染
- 动画使用requestAnimationFrame
- 批量绘制相同样式的元素

**核心原则4 - 多Canvas处理（防止第二个不显示）**：
- 不同的Canvas name
- 延迟初始化第二个Canvas
- 独立的变量名
- resize事件中绘制

**核心原则5 - 设备兼容性（生产环境必须）**：
- onShow中处理Android息屏恢复
- 使用measureText获取文本实际宽度
- 包含调试信息显示功能

**🚨 AI生成Canvas代码时的强制检查清单**：
□ 1. DPR处理：canvas.width = width * pixelRatio
□ 2. DPR处理：ctx.scale(pixelRatio, pixelRatio)
□ 3. 智能缩放：使用calculateAdaptiveScale函数或CanvasContentScaler类
□ 4. 内容居中：定义ORIGINAL_BOUNDS并使用坐标系统变换
□ 5. 触摸坐标转换：正确处理变换后的触摸事件坐标
□ 6. 性能优化：复杂内容使用离屏渲染
□ 7. 生命周期：onUnload中dispose和cancelAnimationFrame
□ 8. 兼容性：onShow中重绘处理
□ 9. 多Canvas：不同name和延迟初始化
□ 10. 调试支持：包含drawDebugInfo函数
□ 11. 响应式设计：监听resize事件并重新计算变换参数

**记住**：AI生成的Lynx Canvas代码必须是高质量、高性能、跨设备兼容的完整解决方案！

**🎯 Canvas内容缩放与居中定位总结**：
通过遵循以上最佳实践，可以创建在各种屏幕尺寸上都表现良好的Canvas应用：
- **自适应缩放**：内容会根据画布尺寸自动缩放，保持合适的大小
- **居中显示**：内容会居中显示在画布中，提升视觉体验
- **保持原始代码结构**：通过坐标系统变换，无需修改绘图函数的内部逻辑
- **正确处理交互**：触摸事件坐标正确转换，保持交互功能正常工作
- **保留设备像素比处理**：确保在高分辨率设备上渲染清晰

4. **息屏后Canvas白屏（Android）**：
- 原因：TextureView context被清除
- 解决：在onShow中重新设置visibility

5. **Canvas尺寸为0导致渲染异常**：
- 原因：未正确设置width/height
- 解决：确保Canvas Element尺寸设置正确

**Canvas性能优化规则**：
- 使用Canvas-NG（Krypton引擎）获得GPU线程渲染
- 复杂静态内容使用离屏Canvas预渲染
- 批量处理相同状态的绘制操作
- 及时调用dispose()释放资源
- 在onHide中暂停资源，onShow中恢复

**Canvas高级绘图系统 (原生渲染) - 参考LynxCanvasAudio.ts获取完整示例**

🔍 关键DPR处理要点：
- Canvas元素尺寸：canvas.width = width * pixelRatio（物理像素）
- 绘图操作：使用逻辑尺寸（width, height），不再乘以pixelRatio
- 触摸坐标：logicalX = touch.clientX / pixelRatio（物理→逻辑转换）

🔧 CRITICAL: Canvas 查询规则（基于 Lynx 框架特性）

**Canvas 元素查询的根本问题**：
Lynx 框架虽然借鉴了许多 Web 开发的概念，但它拥有自己独特的 API 和实现规范。一个常见的错误来源是开发者将标准的 Web API 使用习惯直接应用于 Lynx 环境，而未查阅其特定文档，从而导致兼容性问题。

**核心根源**：
开发者试图使用标准的 CSS ID 选择器（例如 #my-canvas）来查询一个需要通过 Lynx 框架特定选择器才能定位的 Canvas 组件。
在 Lynx 中，<canvas> 组件的标识符是 canvas-id，它并非 HTML 标准中的 id 属性。因此，标准的 document.querySelector('#id') 或 Lynx 提供的 query.select('#id') 语法无法正确定位到该组件。


❌ 错误示例 - 使用标准 CSS ID 选择器：
\`\`\`javascript
Page({
  onReady() {
    const query = lynx.createSelectorQuery();

    // 错误：使用 CSS ID 选择器 '#some-canvas-id'
    query.select('#some-canvas-id').fields({ node: true, size: true }).exec((res) => {
      if (res && res[0]) {
        const canvas = res[0].node;
        const ctx = canvas.getContext('2d');
        // ...后续绘图操作将失败，因为 canvas 为空
      } else {
        console.error("获取 Canvas 节点失败！");
      }
    });
  }
})
\`\`\`

✅ 正确示例 使用canvas.attachToCanvasView绑定到TTML的Canvas视图
**TTML结构**：
\`\`\`html
<!-- Canvas 动画区域 -->
<canvas
  name="canvas-llm"
  class="canvas-llm"
></canvas>
\`\`\`

**JavaScript初始化**：
\`\`\`javascript
// 初始化Canvas
setupCanvas() {
  console.log('Setting up canvas...');
  try {
    const canvas = lynx.createCanvasNG();

    // 重要：resize事件监听必须在绑定前设置
    canvas.addEventListener("resize", ({ width, height }) => {
      console.log('Canvas resize event:', width, height);
      canvas.width = width * SystemInfo.pixelRatio;
      canvas.height = height * SystemInfo.pixelRatio;
      const ctx = canvas.getContext('2d');
      ctx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio);
      this.canvas = canvas;
      this.ctx = ctx;
      this.canvasWidth = width;
      this.canvasHeight = height;
      console.log('Canvas setup complete, starting animation...');
      this.startAnimation();
    });

    // 绑定到Canvas视图
    canvas.attachToCanvasView("canvas-llm");
  } catch (error) {
    console.error('Canvas setup failed:', error);
  }
}
\`\`\`

**完整的DPR处理示例代码**：
\`\`\`javascript
// 标准Canvas初始化流程 - 完整DPR处理
setupCanvas() {
  console.log('Setting up canvas...');
  try {
    const canvas = lynx.createCanvasNG();

    // 重要：resize事件监听必须在绑定前设置
    canvas.addEventListener("resize", ({ width, height }) => {
      console.log('Canvas resize event:', width, height);

      // 🔍 设备像素比(DPR)处理 - 阶段1：初始化（乘以pixelRatio）
      const pixelRatio = SystemInfo.pixelRatio || 1;
      canvas.width = width * pixelRatio;   // 设置物理像素宽度
      canvas.height = height * pixelRatio; // 设置物理像素高度

      // 🔍 设备像素比(DPR)处理 - 阶段2：绘图（应用缩放后使用逻辑尺寸）
      const ctx = canvas.getContext('2d');
      ctx.scale(pixelRatio, pixelRatio);   // 应用缩放

      // 保存引用（重要：保存逻辑尺寸用于绘图操作）
      this.canvas = canvas;
      this.ctx = ctx;
      this.canvasWidth = width;   // 逻辑宽度（不乘pixelRatio）
      this.canvasHeight = height; // 逻辑高度（不乘pixelRatio）
      this.pixelRatio = pixelRatio;

      console.log('Canvas setup complete, starting animation...');
      this.startAnimation();
    });

    // 绑定到Canvas视图
    canvas.attachToCanvasView("canvas-name");
  } catch (error) {
    console.error('Canvas setup failed:', error);
  }
}

// DPR处理的绘图示例
drawContent() {
  const { ctx, canvasWidth, canvasHeight } = this;
  if (!ctx) return;

  // 🔍 重要：使用逻辑尺寸（canvasWidth, canvasHeight），不使用canvas.width/canvas.height

  // 清除画布（使用逻辑尺寸）
  ctx.clearRect(0, 0, canvasWidth, canvasHeight);

  // 绘制背景（使用逻辑尺寸）
  ctx.fillStyle = '#f8f9fa';
  ctx.fillRect(0, 0, canvasWidth, canvasHeight);

  // 绘制文本（使用逻辑尺寸）
  const fontSize = Math.max(12, Math.min(canvasWidth, canvasHeight) / 30);
  ctx.font = \`\${fontSize}px sans-serif\`;  // 不需要乘以pixelRatio
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillStyle = '#333';
  ctx.fillText('Hello Canvas', canvasWidth / 2, canvasHeight / 2);

  // 绘制圆形（使用逻辑尺寸）
  ctx.beginPath();
  ctx.arc(canvasWidth / 2, canvasHeight / 2 + fontSize * 2, fontSize, 0, Math.PI * 2);
  ctx.fillStyle = '#007bff';
  ctx.fill();
}

// 触摸事件处理 - 正确的DPR坐标转换
setupCanvasInteraction() {
  this.canvas.addEventListener("touchstart", (event) => {
    if (event.changedTouches) {
      const touch = event.changedTouches[0];

      // 🔍 正确的坐标转换：从物理像素转换为逻辑像素
      const logicalX = touch.clientX / this.pixelRatio;
      const logicalY = touch.clientY / this.pixelRatio;

      // 检查是否在Canvas范围内（使用逻辑尺寸）
      const isInBounds = logicalX >= 0 && logicalX <= this.canvasWidth &&
                        logicalY >= 0 && logicalY <= this.canvasHeight;

      if (isInBounds) {
        this.onTouchCanvas(logicalX, logicalY);
      }
    }
  });
}
\`\`\`

**Canvas 视觉增强技术**：

**实用视觉增强**：
- 信息可视化：数据驱动的图表、图形、指示器
- 状态反馈：加载进度、操作状态、错误提示
- 导航辅助：高亮、指引、路径标识
- 内容组织：分组框架、连接线、层次标识

**精美动画效果**：
- 过渡动画：状态切换的平滑过渡，300-500ms
- 反馈动画：点击确认、悬停提示、拖拽跟随
- 引导动画：新功能介绍、操作提示
- 数据动画：图表更新、数值变化展示

**移动端优化要求**：
- 卡片样式优化：为主要指标卡片和图表背景添加圆角和渐变和高光效果
- 趋势图表增强：
  - 增加Y轴的网格线和刻度标签，使数据更易于解读
  - 优化数据点和标签的显示逻辑，禁止出现文字重叠
  - 调整图表的内边距和整体布局，使其不那么拥挤
  - 图表需要增加详细的图例说明，包含各项的名称、数值和百分比
- 动态字体大小：标题和标签的字体大小，要根据画布的宽度和高度进行计算，确保在画布缩放时文字大小能相应调整
- 最小字体限制：为字体大小设置一个最小值（12px），防止在画布过小时文字变得难以阅读
- 相对布局：标签的X、Y位置以及行高也要相对于画布尺寸和字体大小进行计算，使得整体布局更具适应性

**Canvas 开发关键点**：
- 必须显式绑定/解绑: attachToCanvasView/detachFromCanvasView
- 通过addEventListener("resize")获取实际尺寸并更新canvas宽高
- 销毁时必须解绑并清理引用
- 增加充分的 try catch 和属性 fallback 以防止兼容性错误，并打印充足的 console.log 进行 debug
- 标签使用name="canvas-llm"（不是id）
- 所有操作乘以SystemInfo.pixelRatio
- 避免新Canvas API（如roundRect和globalCompositeOperation）

🔥 **Canvas 查询规则总结**：
**永远记住**：在 Lynx 框架中进行 Canvas 查询时：
1. **TTML 中使用**：canvas-id="your-canvas-id" 属性
2. **JavaScript 查询时使用**：'canvasId=your-canvas-id' 选择器语法
3. **绝对不要使用**：'#your-canvas-id' CSS ID 选择器
4. **这是 Lynx 框架的特有实现**，与标准 Web API 不同，必须严格遵循

**记忆口诀**：canvas-id 对应 canvasId=，查询成功靠语法！

🚨🚨🚨 CLAUDE4 CANVAS错误防范总结 🚨🚨🚨

**最高频错误 - 错误的Canvas初始化模式**：
❌ Claude4经常犯的错误：
\`\`\`javascript
// 错误示例：缺少关键步骤的Canvas初始化
initRouteCanvas() {
  const canvas = lynx.createCanvasNG("routeCanvas");  // ❌ 错误1：传参数
  if (!canvas) return;

  const ctx = canvas.getContext("2d");
  const width = canvas.width;                         // ❌ 错误2：没有resize监听
  const height = canvas.height;                       // ❌ 错误3：没有pixelRatio适配

  ctx.clearRect(0, 0, width, height);                 // ❌ 错误4：没有attachToCanvasView
  ctx.fillStyle = "#f8f9fa";
  ctx.fillRect(0, 0, width, height);
  this.drawSilkRoadMap(ctx, width, height);
}

// 或者使用已废弃的API
const canvas = lynx.createCanvas("canvasName");       // ❌ 错误5：使用废弃API
\`\`\`

✅ 正确的Canvas初始化：
\`\`\`javascript
setupCanvas() {
  console.log('Setting up canvas...');
  try {
    const canvas = lynx.createCanvasNG();

    canvas.addEventListener("resize", ({ width, height }) => {
      console.log('Canvas resize event:', width, height);
      canvas.width = width * SystemInfo.pixelRatio;
      canvas.height = height * SystemInfo.pixelRatio;
      const ctx = canvas.getContext('2d');
      ctx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio);
      this.canvas = canvas;
      this.ctx = ctx;
      this.canvasWidth = width;
      this.canvasHeight = height;
      this.startAnimation();
    });

    canvas.attachToCanvasView("canvas-llm");
  } catch (error) {
    console.error('Canvas setup failed:', error);
  }
}
\`\`\`

✅ 强制要求的正确模式：
\`\`\`javascript
// 正确示例：完整的Canvas初始化流程
initRouteCanvas() {
  // 步骤1：创建Canvas Element（无参数）
  const canvas = lynx.krypton.createCanvasNG();

  // 步骤2：设置resize事件监听（必须在绑定前）
  canvas.addEventListener('resize', ({ width, height }) => {
    // 步骤3：pixelRatio适配（防止模糊）
    canvas.width = width * SystemInfo.pixelRatio;
    canvas.height = height * SystemInfo.pixelRatio;
    const ctx = canvas.getContext('2d');
    ctx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio);

    // 绘制逻辑
    this.drawSilkRoadMap(ctx, width, height);
  });

  // 步骤4：绑定到Canvas View
  canvas.attachToCanvasView('routeCanvas');
},

drawSilkRoadMap(ctx, width, height) {
  ctx.clearRect(0, 0, width, height);
  ctx.fillStyle = "#f8f9fa";
  ctx.fillRect(0, 0, width, height);
  // 其他绘制逻辑...
}
\`\`\`

**🔥 强制检查清单 - Canvas和LightChart分离验证**：

**原生Canvas专用检查（使用setupCanvas()）**：
□ 1. lynx.createCanvasNG() - 无参数创建（不是lynx.createCanvas）
□ 2. addEventListener('resize') - resize事件监听在绑定前设置
□ 3. attachToCanvasView(name) - 绑定到Canvas View
□ 4. 使用setupCanvas()方法进行初始化
□ 5. 绘制逻辑在resize回调中执行
□ 6. 🚨 确认没有LightChart相关代码（new LynxChart、chart.setOption等）

**🔍 设备像素比(DPR)处理专项检查**：
□ 7. 阶段1-初始化：canvas.width = width * SystemInfo.pixelRatio
□ 8. 阶段1-初始化：canvas.height = height * SystemInfo.pixelRatio
□ 9. 阶段2-绘图：ctx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio)
□ 10. 保存逻辑尺寸：this.canvasWidth = width（不乘pixelRatio）
□ 11. 保存逻辑尺寸：this.canvasHeight = height（不乘pixelRatio）
□ 12. 绘图操作使用逻辑尺寸：ctx.clearRect(0, 0, width, height)
□ 13. 文本渲染使用逻辑尺寸：ctx.font = \`\${fontSize}px sans-serif\`
□ 14. 触摸坐标转换：logicalX = touch.clientX / pixelRatio
□ 15. 🚨 确认绘图操作中没有再次乘以pixelRatio

**LightChart专用检查（使用initChart()）**：
□ 1. import LynxChart from "@byted/lynx-lightcharts/src/chart"
□ 2. initChart(e) 方法接收事件参数
□ 3. new LynxChart({ canvasName, width, height })
□ 4. chart.setOption(option) 配置图表
□ 5. chart.destroy() 在onUnload中销毁
□ 6. <lightcharts-canvas> 标签使用
□ 7. 🚨 确认没有原生Canvas相关代码（lynx.createCanvasNG、setupCanvas等）

**混用检测（必须为空）**：
□ 8. 🔥 确认同一Card中没有setupCanvas() AND initChart()
□ 9. 🔥 确认同一Card中没有原生Canvas AND LightChart API

**违反任何一条都会导致**：
- Canvas不显示或显示异常
- 尺寸错误或模糊显示
- resize时不重绘
- 高分屏适配失败

**记住**：Canvas初始化是4步骤流程，缺一不可！`;
