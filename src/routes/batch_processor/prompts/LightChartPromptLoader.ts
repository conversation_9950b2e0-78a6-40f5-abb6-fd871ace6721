/**
 * LightChart 完美生成指南 - 基于Claude 4认知科学优化的提示词
 * 设计原则：简洁、清晰、一次性成功
 */

export const LIGHTCHART_PROMPT_CONTENT = `
# LightChart 完美生成指南 (Claude 4 专用)

## 🎯 核心原则：图表类型决定一切
**关键认知**: LightChart 不是 ECharts，每种图表类型有独特的配置模式
**执行流程**: 识别图表类型 → 选择对应模板 → 严格执行 → 验证输出

## 📊 三种图表类型的配置模式

### 🥧 PIE 图表模式
**识别标志**: type: 'pie'
**数据模式**: series.data + encode
**核心配置**:
- ✅ radius: ['0%', '80%'] (必需)
- ✅ series.data: [{ name: 'A', value: 10 }]
- ✅ encode: { name: 'name', value: 'value' }
- ❌ 绝对禁止: size属性、option.data、xAxis/yAxis

### 📊 BAR/LINE 图表模式
**识别标志**: type: 'bar' 或 type: 'line'
**数据模式**: option.data + series.encode
**核心配置**:
- ✅ option.data: [...] (全局数据)
- ✅ series.encode: { x: 'field1', y: 'field2' }
- ✅ xAxis: [{ type: 'category' }] (数组格式)
- ✅ yAxis: [{ type: 'value' }] (数组格式)
- ❌ 绝对禁止: series.data、对象格式的轴配置

### 🔄 混合图表模式
**识别标志**: 同时有 type: 'bar' 和 type: 'line'
**特殊规则**: BAR系列不能配置 shapeStyle
**核心配置**:
- ✅ colors: ['#color1', '#color2'] (统一颜色)
- ✅ BAR系列: { type: 'bar', encode: {...} } (无shapeStyle)
- ✅ LINE系列: { type: 'line', marker: { shapeStyle: {...} } }
- ❌ 绝对禁止: BAR系列的 shapeStyle 配置

## 🔧 样式配置规则

### 颜色配置
- **PIE图表**: colors数组
- **单BAR图表**: series.shapeStyle配置
- **混合图表**: colors数组 (BAR系列不用shapeStyle)
- **LINE图表**: marker.shapeStyle配置

### Label配置
- **推荐**: 移除formatter，使用默认行为
- **禁止**: ECharts语法 formatter
- **禁止**: 换行符语法 formatter

### Tooltip配置
- **推荐**: tooltip基础配置
- **禁止**: 自定义formatter (使用默认行为)

## 🏗️ 标准代码模板

### PIE图表模板
STRUCTURE: colors数组 + series.data + encode + radius
EXAMPLE: colors: ['#color1', '#color2'], series: [{ type: 'pie', radius: ['0%', '80%'], data: [...], encode: {...} }]

### BAR图表模板
STRUCTURE: colors数组 + option.data + series.encode + 轴数组
EXAMPLE: colors: [...], data: [...], xAxis: [{}], yAxis: [{}], series: [{ type: 'bar', encode: {...} }]

### 混合图表模板
STRUCTURE: colors数组 + option.data + 双Y轴 + BAR系列(无shapeStyle) + LINE系列(有marker.shapeStyle)
EXAMPLE: colors: [...], yAxis: [{}, { opposite: true }], series: [{ type: 'bar', yAxisIndex: 0 }, { type: 'line', yAxisIndex: 1, marker: { shapeStyle: {...} } }]

## ✅ 生成前检查清单
1. **图表类型识别**: 确定是PIE、BAR、LINE还是混合图表
2. **数据模式选择**: PIE用series.data，其他用option.data
3. **样式配置验证**: 混合图表BAR系列不用shapeStyle
4. **轴配置检查**: 使用数组格式，不用对象格式
5. **Label简化**: 移除formatter，使用默认行为

## 🚨 绝对禁止的配置
- PIE图表使用size属性
- 混合图表BAR系列配置shapeStyle
- 使用ECharts的模板语法
- 轴配置使用对象格式而非数组格式
- 混用不同图表类型的数据模式

## 🎯 执行成功保证
通过以上图表类型专用规则，Claude 4 将能够：
1. 准确识别图表类型并选择对应配置模式
2. 避免混用不同图表类型的语法规则
3. 生成100%正确的LightChart代码
4. 一次性通过，无需调试修改

## 🔄 标准执行流程
1. **识别图表类型** → 确定是PIE、BAR、LINE还是混合
2. **选择配置模式** → 使用对应的数据模式和样式规则
3. **应用标准模板** → 按照模板结构生成代码
4. **执行检查清单** → 验证所有配置符合规则
5. **输出完美代码** → 确保一次性成功运行

## 🚀 环境检测模板
每个图表初始化前必须包含：
- 检测 lynx 和 lynx.krypton 存在
- 检测 SystemInfo 存在
- 参数验证：canvasName、width、height
- 异步调用安全：使用存在性检查

## 📋 三文件结构要求
- index.json: 注册 lightcharts-canvas 组件
- index.ttml: 使用 lightcharts-canvas 标签
- index.js: 导入 LynxChart 并正确初始化

通过这个简洁而完整的指南，Claude 4 能够一次性生成完美的 LightChart 代码，避免所有常见错误。
`;

export default {
  LIGHTCHART_PROMPT_CONTENT,
};
